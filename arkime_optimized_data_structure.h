/*
 * Arkime 文件位置信息优化数据结构
 * 针对10000+数量级的性能优化方案
 */

#ifndef ARKIME_OPTIMIZED_DATA_STRUCTURE_H
#define ARKIME_OPTIMIZED_DATA_STRUCTURE_H

#include <stdint.h>
#include <stdlib.h>

// 方案1：动态数组 + 分块策略
#define ARKIME_CHUNK_SIZE 1024  // 每个块的大小

struct arkime_file_pos_chunk {
    int64_t *data;                           // 数据数组
    uint32_t count;                          // 当前块中的元素数量
    uint32_t capacity;                       // 当前块的容量
    struct arkime_file_pos_chunk *next;      // 下一个块
};

struct arkime_file_pos_array {
    struct arkime_file_pos_chunk *head;      // 第一个块
    struct arkime_file_pos_chunk *tail;      // 最后一个块（用于快速插入）
    uint64_t total_count;                    // 总元素数量
    uint64_t file_count;                     // 文件数量
    uint64_t current_file_num;               // 当前文件标识
};

// 方案2：预分配大数组 + 动态扩容
struct arkime_file_pos_vector {
    int64_t *data;                           // 数据数组
    uint32_t count;                          // 当前元素数量
    uint32_t capacity;                       // 当前容量
    uint64_t file_count;                     // 文件数量
    uint64_t current_file_num;               // 当前文件标识
};

// 方案3：混合结构 - 文件索引 + 位置数组
struct arkime_file_entry {
    uint64_t file_num;                       // 文件号
    uint32_t start_index;                    // 在位置数组中的起始索引
    uint32_t count;                          // 该文件的位置数量
};

struct arkime_file_pos_hybrid {
    int64_t *positions;                      // 位置数组
    uint32_t pos_count;                      // 位置数量
    uint32_t pos_capacity;                   // 位置数组容量
    
    struct arkime_file_entry *files;        // 文件索引数组
    uint32_t file_count;                     // 文件数量
    uint32_t file_capacity;                  // 文件数组容量
    
    uint64_t current_file_num;               // 当前文件标识
};

// 函数声明

// 方案1：分块数组
int arkime_chunk_array_init(struct arkime_file_pos_array *array);
int arkime_chunk_array_add(struct arkime_file_pos_array *array, uint64_t file_num, uint64_t file_pos);
int arkime_chunk_array_to_linear(struct arkime_file_pos_array *array, int64_t **out_array, int *out_size);
void arkime_chunk_array_clear(struct arkime_file_pos_array *array);

// 方案2：动态向量
int arkime_vector_init(struct arkime_file_pos_vector *vector);
int arkime_vector_add(struct arkime_file_pos_vector *vector, uint64_t file_num, uint64_t file_pos);
int arkime_vector_to_array(struct arkime_file_pos_vector *vector, int64_t **out_array, int *out_size);
void arkime_vector_clear(struct arkime_file_pos_vector *vector);

// 方案3：混合结构
int arkime_hybrid_init(struct arkime_file_pos_hybrid *hybrid);
int arkime_hybrid_add(struct arkime_file_pos_hybrid *hybrid, uint64_t file_num, uint64_t file_pos);
int arkime_hybrid_to_array(struct arkime_file_pos_hybrid *hybrid, int64_t **out_array, int *out_size);
void arkime_hybrid_clear(struct arkime_file_pos_hybrid *hybrid);

#endif /* ARKIME_OPTIMIZED_DATA_STRUCTURE_H */

/*
 * 性能对比分析（10000+元素）：
 * 
 * 1. 原始链表：
 *    - 插入：O(1) 但缓存命中率低
 *    - 遍历：O(n) 且内存跳转频繁
 *    - 内存：每个元素24字节（8+8+8）
 *    - 适用：小规模数据
 * 
 * 2. 分块数组（推荐）：
 *    - 插入：O(1) 摊销，缓存友好
 *    - 遍历：O(n) 但连续内存访问
 *    - 内存：每个元素8字节 + 少量块开销
 *    - 适用：大规模数据，平衡内存和性能
 * 
 * 3. 动态向量：
 *    - 插入：O(1) 摊销
 *    - 遍历：O(n) 最佳缓存性能
 *    - 内存：每个元素8字节 + 扩容开销
 *    - 适用：已知大致规模的场景
 * 
 * 4. 混合结构：
 *    - 插入：O(1)
 *    - 查询：O(log n) 按文件查询
 *    - 内存：最优，分离索引和数据
 *    - 适用：需要按文件快速查询的场景
 */
